include "root" {
  path = find_in_parent_folders()
}

include "deployment" {
  path = find_in_parent_folders("deployment.hcl")
}

include "area" {
  path = find_in_parent_folders("area.hcl")
}

include "region" {
  path = find_in_parent_folders("region.hcl")
}

terraform {
  source = "../../../../../terraform/modules/database/rds"
}

dependency "vpc" {
  config_path = "../vpc"
  
  mock_outputs = {
    vpc_id             = "vpc-12345678"
    private_subnet_ids = ["subnet-12345678", "subnet-87654321"]
  }
}

dependency "security_groups" {
  config_path = "../security-groups"
  
  mock_outputs = {
    rds_postgres_sg_id = "sg-12345678"
  }
}

locals {
  deployment_vars = read_terragrunt_config(find_in_parent_folders("deployment.hcl"))
  area_vars       = read_terragrunt_config(find_in_parent_folders("area.hcl"))
  region_vars     = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  
  deployment = local.deployment_vars.locals.deployment
  project    = local.deployment_vars.locals.project
  area       = local.area_vars.locals.area
  region     = local.region_vars.locals.aws_region
}

inputs = {
  project_name       = local.project
  environment        = local.deployment
  vpc_id             = dependency.vpc.outputs.vpc_id
  private_subnet_ids = dependency.vpc.outputs.private_subnet_ids
  security_group_ids = [dependency.security_groups.outputs.rds_postgres_sg_id]
  
  db_name     = "baiker_dev_db"
  db_username = "postgres"
  db_port     = 5434
  
  engine_version        = "17.4"
  instance_class        = "db.m7g.large"
  allocated_storage     = 100
  max_allocated_storage = 300
  storage_type          = "gp3"
  iops                  = 3000
  storage_throughput    = 125
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  deletion_protection    = false
  
  common_tags = local.deployment_vars.locals.common_tags
}
