include "root" {
  path = find_in_parent_folders()
}

include "deployment" {
  path = find_in_parent_folders("deployment.hcl")
}

include "area" {
  path = find_in_parent_folders("area.hcl")
}

include "region" {
  path = find_in_parent_folders("region.hcl")
}

terraform {
  source = "../../../../../terraform/modules/security/cognito"
}

locals {
  deployment_vars = read_terragrunt_config(find_in_parent_folders("deployment.hcl"))
  area_vars       = read_terragrunt_config(find_in_parent_folders("area.hcl"))
  region_vars     = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  
  deployment = local.deployment_vars.locals.deployment
  project    = local.deployment_vars.locals.project
  area       = local.area_vars.locals.area
  region     = local.region_vars.locals.aws_region
}

inputs = {
  project_name    = local.project
  environment     = local.deployment
  user_pool_name  = "${local.project}-${local.deployment}-cognito"
  app_client_name = "${local.project}-${local.deployment}-cognito-app-client"
  
  alias_attributes         = ["email", "preferred_username"]
  auto_verified_attributes = ["email"]
  
  authentication_flow_session_duration = 3
  refresh_token_validity               = 5
  access_token_validity                = 60
  id_token_validity                   = 60
  
  enable_token_revocation         = true
  prevent_user_existence_errors   = true
  
  oauth_scopes = ["email", "profile", "openid"]
  oauth_flows  = ["code"]
  
  explicit_auth_flows = [
    "ALLOW_USER_AUTH",
    "ALLOW_USER_SRP_AUTH", 
    "ALLOW_REFRESH_TOKEN_AUTH"
  ]
  
  common_tags = local.deployment_vars.locals.common_tags
}
