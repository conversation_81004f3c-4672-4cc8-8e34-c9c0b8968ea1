include "root" {
  path = find_in_parent_folders()
}

include "deployment" {
  path = find_in_parent_folders("deployment.hcl")
}

include "area" {
  path = find_in_parent_folders("area.hcl")
}

include "region" {
  path = find_in_parent_folders("region.hcl")
}

terraform {
  source = "../../../../../terraform/modules/networking/vpc"
}

locals {
  deployment_vars = read_terragrunt_config(find_in_parent_folders("deployment.hcl"))
  area_vars       = read_terragrunt_config(find_in_parent_folders("area.hcl"))
  region_vars     = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  
  deployment = local.deployment_vars.locals.deployment
  project    = local.deployment_vars.locals.project
  area       = local.area_vars.locals.area
  region     = local.region_vars.locals.aws_region
}

inputs = {
  name                   = "${local.project}-${local.deployment}-vpc"
  cidr_block            = "10.0.0.0/16"
  availability_zones    = ["${local.region}a", "${local.region}c"]
  public_subnet_cidrs   = ["********/24", "********/24"]
  private_subnet_cidrs  = ["*********/24", "*********/24", "*********/24", "*********/24"]
  
  enable_dns_hostnames  = true
  enable_dns_support    = true
  enable_nat_gateway    = true
  single_nat_gateway    = true
  
  common_tags = local.deployment_vars.locals.common_tags
}
