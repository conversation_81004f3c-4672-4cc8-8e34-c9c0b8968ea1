include "root" {
  path = find_in_parent_folders()
}

include "deployment" {
  path = find_in_parent_folders("deployment.hcl")
}

include "area" {
  path = find_in_parent_folders("area.hcl")
}

include "region" {
  path = find_in_parent_folders("region.hcl")
}

terraform {
  source = "../../../../../terraform/modules/storage/s3"
}

locals {
  deployment_vars = read_terragrunt_config(find_in_parent_folders("deployment.hcl"))
  area_vars       = read_terragrunt_config(find_in_parent_folders("area.hcl"))
  region_vars     = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  
  deployment = local.deployment_vars.locals.deployment
  project    = local.deployment_vars.locals.project
  area       = local.area_vars.locals.area
  region     = local.region_vars.locals.aws_region
}

inputs = {
  bucket_names = [
    "${local.project}-${local.deployment}-s3-terraform-state",
    "${local.project}-${local.deployment}-s3-raw-data"
  ]
  
  project_name    = local.project
  environment     = local.deployment
  
  enable_versioning      = false
  enable_encryption      = true
  block_public_access    = true
  enable_lifecycle_policy = false
  
  common_tags = local.deployment_vars.locals.common_tags
}
