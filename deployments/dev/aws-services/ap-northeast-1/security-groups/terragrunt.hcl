include "root" {
  path = find_in_parent_folders()
}

include "deployment" {
  path = find_in_parent_folders("deployment.hcl")
}

include "area" {
  path = find_in_parent_folders("area.hcl")
}

include "region" {
  path = find_in_parent_folders("region.hcl")
}

terraform {
  source = "../../../../../terraform/modules/security/security-groups"
}

dependency "vpc" {
  config_path = "../vpc"
  
  mock_outputs = {
    vpc_id = "vpc-12345678"
  }
}

locals {
  deployment_vars = read_terragrunt_config(find_in_parent_folders("deployment.hcl"))
  area_vars       = read_terragrunt_config(find_in_parent_folders("area.hcl"))
  region_vars     = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  
  deployment = local.deployment_vars.locals.deployment
  project    = local.deployment_vars.locals.project
  area       = local.area_vars.locals.area
  region     = local.region_vars.locals.aws_region
}

inputs = {
  vpc_id           = dependency.vpc.outputs.vpc_id
  project_name     = local.project
  environment      = local.deployment
  allowed_ssh_cidr = "103.199.7.223/32"
  
  common_tags = local.deployment_vars.locals.common_tags
}
