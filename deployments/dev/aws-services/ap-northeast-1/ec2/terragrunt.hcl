include "root" {
  path = find_in_parent_folders()
}

include "deployment" {
  path = find_in_parent_folders("deployment.hcl")
}

include "area" {
  path = find_in_parent_folders("area.hcl")
}

include "region" {
  path = find_in_parent_folders("region.hcl")
}

terraform {
  source = "../../../../../terraform/modules/compute/ec2"
}

dependency "vpc" {
  config_path = "../vpc"
  
  mock_outputs = {
    vpc_id            = "vpc-12345678"
    public_subnet_ids = ["subnet-12345678", "subnet-87654321"]
  }
}

dependency "security_groups" {
  config_path = "../security-groups"
  
  mock_outputs = {
    main_sg_id        = "sg-12345678"
    ssh_bastion_sg_id = "sg-87654321"
  }
}

locals {
  deployment_vars = read_terragrunt_config(find_in_parent_folders("deployment.hcl"))
  area_vars       = read_terragrunt_config(find_in_parent_folders("area.hcl"))
  region_vars     = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  
  deployment = local.deployment_vars.locals.deployment
  project    = local.deployment_vars.locals.project
  area       = local.area_vars.locals.area
  region     = local.region_vars.locals.aws_region
}

inputs = {
  project_name              = local.project
  environment              = local.deployment
  vpc_id                   = dependency.vpc.outputs.vpc_id
  public_subnet_ids        = dependency.vpc.outputs.public_subnet_ids
  bastion_security_group_id = dependency.security_groups.outputs.ssh_bastion_sg_id
  main_security_group_id   = dependency.security_groups.outputs.main_sg_id
  
  ami_id                = "ami-054400ced365b82a0"
  bastion_instance_type = "t2.micro"
  main_instance_type    = "t2.medium"
  key_pair_name         = "${local.project}-${local.deployment}-key-pair-ec2"
  
  common_tags = local.deployment_vars.locals.common_tags
}
