include "root" {
  path = find_in_parent_folders()
}

include "deployment" {
  path = find_in_parent_folders("deployment.hcl")
}

include "area" {
  path = find_in_parent_folders("area.hcl")
}

include "region" {
  path = find_in_parent_folders("region.hcl")
}

terraform {
  source = "../../../../../terraform/modules/compute/ecr"
}

locals {
  deployment_vars = read_terragrunt_config(find_in_parent_folders("deployment.hcl"))
  area_vars       = read_terragrunt_config(find_in_parent_folders("area.hcl"))
  region_vars     = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  
  deployment = local.deployment_vars.locals.deployment
  project    = local.deployment_vars.locals.project
  area       = local.area_vars.locals.area
  region     = local.region_vars.locals.aws_region
}

inputs = {
  repository_name      = local.project
  namespace           = local.deployment
  image_tag_mutability = "MUTABLE"
  encryption_type     = "AES256"
  scan_on_push        = true
  
  common_tags = local.deployment_vars.locals.common_tags
}
