# Root terragrunt.hcl
# This file contains common configuration for all environments

locals {
  # Parse the file path to extract environment and region information
  path_parts = split("/", path_relative_to_include())
  
  # Default values
  deployment = try(local.path_parts[1], "dev")
  area       = try(local.path_parts[2], "aws-services")
  region     = try(local.path_parts[3], "ap-northeast-1")
}

# Generate common variables for all modules
generate "common_vars" {
  path      = "common_vars.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "${local.region}"
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}
EOF
}

# Configure Terraform settings
terraform {
  extra_arguments "common_vars" {
    commands = get_terraform_commands_that_need_vars()
    
    arguments = [
      "-var", "aws_region=${local.region}"
    ]
  }
}
