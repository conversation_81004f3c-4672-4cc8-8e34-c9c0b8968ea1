# Baiker Infrastructure as Code

This repository contains Terraform modules and Terragrunt configurations for deploying the Baiker application infrastructure on AWS.

## Architecture Overview

The infrastructure includes:
- **VPC**: Custom VPC with public/private subnets across 2 AZs
- **EC2**: Bastion host and main application instance
- **RDS**: PostgreSQL database with managed credentials
- **Security Groups**: Fine-grained network access control
- **Load Balancer**: Network Load Balancer with multiple target groups
- **ECR**: Container registry for application images
- **Cognito**: User authentication and authorization
- **S3**: Storage for Terraform state and raw data

## Directory Structure

```
.
├── README.md
├── deployments/          # Terragrunt configurations by environment
│   ├── dev/              # Development environment
│   ├── infra/            # Infrastructure environment
│   └── prod/             # Production environment
└── terraform/
    └── modules/          # Reusable Terraform modules
        ├── api/
        ├── compute/
        ├── database/
        ├── lambda/
        ├── networking/
        ├── security/
        └── storage/
```

## Prerequisites

- [Terraform](https://www.terraform.io/downloads.html) >= 1.0
- [Terragrunt](https://terragrunt.gruntwork.io/docs/getting-started/install/) >= 0.45
- AWS CLI configured with appropriate credentials
- AWS account with necessary permissions

## Quick Start

### Prerequisites Setup

1. **Install Required Tools**
   ```bash
   # Install Terraform
   curl -fsSL https://apt.releases.hashicorp.com/gpg | sudo apt-key add -
   sudo apt-add-repository "deb [arch=amd64] https://apt.releases.hashicorp.com $(lsb_release -cs) main"
   sudo apt-get update && sudo apt-get install terraform

   # Install Terragrunt
   curl -Lo terragrunt https://github.com/gruntwork-io/terragrunt/releases/latest/download/terragrunt_linux_amd64
   chmod +x terragrunt
   sudo mv terragrunt /usr/local/bin/
   ```

2. **Configure AWS Credentials**
   ```bash
   aws configure
   # Enter your AWS Access Key ID, Secret Access Key, and region (ap-northeast-1)
   ```

### Deployment Steps

1. **Clone and Navigate**
   ```bash
   git clone <repository-url>
   cd Entanglement_IaC
   ```

2. **Deploy Infrastructure (Recommended Order)**
   ```bash
   # Navigate to dev environment
   cd deployments/dev/aws-services/ap-northeast-1

   # Deploy in dependency order
   cd vpc && terragrunt apply
   cd ../security-groups && terragrunt apply
   cd ../ec2 && terragrunt apply
   cd ../rds && terragrunt apply
   cd ../load-balancer && terragrunt apply
   cd ../ecr && terragrunt apply
   cd ../cognito && terragrunt apply
   cd ../s3 && terragrunt apply
   ```

3. **Or Deploy All at Once**
   ```bash
   cd deployments/dev/aws-services/ap-northeast-1
   terragrunt run-all plan
   terragrunt run-all apply
   ```

## Environments

- **dev**: Development environment in ap-northeast-1
- **infra**: Infrastructure environment in us-east-1  
- **prod**: Production environment in us-east-1

## Security Considerations

- All resources use least-privilege access principles
- Database credentials are managed by AWS Secrets Manager
- Security groups follow principle of least privilege
- S3 buckets block public access by default

## Infrastructure Components

### Core Infrastructure
- **VPC**: `baiker-dev-vpc` with 10.0.0.0/16 CIDR, 2 AZs, 2 public + 4 private subnets
- **Security Groups**: 5 security groups with specific ingress/egress rules
- **EC2**: Bastion host (t2.micro) + Main instance (t2.medium) with Docker
- **Key Pair**: ED25519 key pair for EC2 access

### Database & Storage
- **RDS**: PostgreSQL 17.4 on db.m7g.large with managed credentials
- **S3**: Two buckets for Terraform state and raw data storage

### Networking & Load Balancing
- **NLB**: Network Load Balancer with 6 target groups
- **Target Groups**: Support for various protocols (TCP/UDP) and health checks

### Security & Authentication
- **Cognito**: User pool with mobile app client configuration
- **ECR**: Container registry with AES-256 encryption

## Resource Naming Convention

All resources follow the pattern: `{project}-{environment}-{resource-type}`
- Project: `baiker`
- Environment: `dev`, `infra`, `prod`
- Examples: `baiker-dev-vpc`, `baiker-dev-ec2`, `baiker-dev-rds`

## Important Notes

### Security Configuration
- SSH access restricted to `*************/32`
- RDS credentials managed by AWS Secrets Manager
- All S3 buckets block public access
- Security groups follow least privilege principle

### Database Configuration
- PostgreSQL port: 5434 (custom)
- Storage: 100 GiB initial, auto-scaling to 300 GiB
- Backup retention: 7 days
- Enhanced monitoring enabled

### EC2 Configuration
- AMI: `ami-054400ced365b82a0` (Ubuntu 24.04)
- Main instance includes Docker installation
- Both instances use encrypted EBS volumes

## Troubleshooting

### Common Issues
1. **Terraform State Conflicts**: Ensure S3 bucket exists before running terragrunt
2. **Dependency Errors**: Deploy in the recommended order or use `terragrunt run-all`
3. **Permission Issues**: Verify AWS credentials have necessary permissions
4. **Region Mismatch**: Ensure all configurations use `ap-northeast-1`

### Useful Commands
```bash
# Check terragrunt configuration
terragrunt validate

# Plan specific module
terragrunt plan

# Show current state
terragrunt show

# Destroy infrastructure (use with caution)
terragrunt destroy
```

## Support

For questions or issues:
1. Check module-specific README files in `terraform/modules/`
2. Review Terragrunt logs for detailed error messages
3. Verify AWS permissions and quotas
4. Contact the infrastructure team
