# Baiker Infrastructure as Code

This repository contains Terraform modules and Terragrunt configurations for deploying the Baiker application infrastructure on AWS.

## Architecture Overview

The infrastructure includes:
- **VPC**: Custom VPC with public/private subnets across 2 AZs
- **EC2**: Bastion host and main application instance
- **RDS**: PostgreSQL database with managed credentials
- **Security Groups**: Fine-grained network access control
- **Load Balancer**: Network Load Balancer with multiple target groups
- **ECR**: Container registry for application images
- **Cognito**: User authentication and authorization
- **S3**: Storage for Terraform state and raw data

## Directory Structure

```
.
├── README.md
├── deployments/          # Terragrunt configurations by environment
│   ├── dev/              # Development environment
│   ├── infra/            # Infrastructure environment
│   └── prod/             # Production environment
└── terraform/
    └── modules/          # Reusable Terraform modules
        ├── api/
        ├── compute/
        ├── database/
        ├── lambda/
        ├── networking/
        ├── security/
        └── storage/
```

## Prerequisites

- [Terraform](https://www.terraform.io/downloads.html) >= 1.0
- [Terragrunt](https://terragrunt.gruntwork.io/docs/getting-started/install/) >= 0.45
- AWS CLI configured with appropriate credentials
- AWS account with necessary permissions

## Quick Start

1. Clone this repository
2. Configure AWS credentials
3. Navigate to the desired environment (e.g., `deployments/dev/`)
4. Run `terragrunt run-all plan` to see planned changes
5. Run `terragrunt run-all apply` to deploy infrastructure

## Environments

- **dev**: Development environment in ap-northeast-1
- **infra**: Infrastructure environment in us-east-1  
- **prod**: Production environment in us-east-1

## Security Considerations

- All resources use least-privilege access principles
- Database credentials are managed by AWS Secrets Manager
- Security groups follow principle of least privilege
- S3 buckets block public access by default

## Support

For questions or issues, please refer to the module-specific README files or contact the infrastructure team.
