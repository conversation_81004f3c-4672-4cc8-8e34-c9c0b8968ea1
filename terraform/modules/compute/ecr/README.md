# ECR Terraform Module

This module creates an Amazon Elastic Container Registry (ECR) repository with configurable settings for image storage and management.

## Features

- ECR repository with namespace support
- Configurable image tag mutability
- AES-256 or KMS encryption
- Image scanning on push
- Lifecycle policies for image cleanup
- Optional repository policies

## Usage

```hcl
module "ecr" {
  source = "../../modules/compute/ecr"

  repository_name      = "baiker"
  namespace           = "dev"
  image_tag_mutability = "MUTABLE"
  encryption_type     = "AES256"
  scan_on_push        = true
  
  common_tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Default Lifecycle Policy

If no custom lifecycle policy is provided, the module creates a default policy that:
- Keeps the last 10 tagged images (with "v" prefix)
- Deletes untagged images older than 1 day

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| repository_name | Name of the ECR repository | `string` | n/a | yes |
| namespace | Namespace for the repository | `string` | `"dev"` | no |
| image_tag_mutability | Image tag mutability setting | `string` | `"MUTABLE"` | no |
| encryption_type | Encryption type for the repository | `string` | `"AES256"` | no |
| kms_key | KMS key ARN for encryption (only used when encryption_type is KMS) | `string` | `null` | no |
| scan_on_push | Enable image scanning on push | `bool` | `true` | no |
| lifecycle_policy | Lifecycle policy for the repository | `string` | `null` | no |
| repository_policy | Repository policy JSON | `string` | `null` | no |
| common_tags | Common tags to apply to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| repository_arn | Full ARN of the repository |
| repository_name | Name of the repository |
| repository_url | URL of the repository |
| registry_id | Registry ID where the repository was created |
| repository_uri | URI of the repository |
| repository_registry_id | Registry ID of the repository |

## Example with Custom Lifecycle Policy

```hcl
module "ecr" {
  source = "../../modules/compute/ecr"

  repository_name      = "baiker"
  namespace           = "dev"
  image_tag_mutability = "MUTABLE"
  encryption_type     = "AES256"
  
  lifecycle_policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep last 5 production images"
        selection = {
          tagStatus     = "tagged"
          tagPrefixList = ["prod-"]
          countType     = "imageCountMoreThan"
          countNumber   = 5
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
  
  common_tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```
