# ECR Repository
resource "aws_ecr_repository" "main" {
  name                 = "${var.namespace}/${var.repository_name}"
  image_tag_mutability = var.image_tag_mutability

  image_scanning_configuration {
    scan_on_push = var.scan_on_push
  }

  encryption_configuration {
    encryption_type = var.encryption_type
    kms_key         = var.kms_key
  }

  tags = merge(var.common_tags, {
    Name      = "${var.namespace}/${var.repository_name}"
    Namespace = var.namespace
  })
}

# Lifecycle Policy
resource "aws_ecr_lifecycle_policy" "main" {
  count      = var.lifecycle_policy != null ? 1 : 0
  repository = aws_ecr_repository.main.name
  policy     = var.lifecycle_policy
}

# Default lifecycle policy if none provided
resource "aws_ecr_lifecycle_policy" "default" {
  count      = var.lifecycle_policy == null ? 1 : 0
  repository = aws_ecr_repository.main.name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep last 10 images"
        selection = {
          tagStatus     = "tagged"
          tagPrefixList = ["v"]
          countType     = "imageCountMoreThan"
          countNumber   = 10
        }
        action = {
          type = "expire"
        }
      },
      {
        rulePriority = 2
        description  = "Delete untagged images older than 1 day"
        selection = {
          tagStatus   = "untagged"
          countType   = "sinceImagePushed"
          countUnit   = "days"
          countNumber = 1
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}

# Repository Policy
resource "aws_ecr_repository_policy" "main" {
  count      = var.repository_policy != null ? 1 : 0
  repository = aws_ecr_repository.main.name
  policy     = var.repository_policy
}
