# EC2 Terraform Module

This module creates EC2 instances including a bastion host and main application instance, along with the required key pair.

## Features

- ED25519 key pair generation
- Bastion host (t2.micro) with basic tools
- Main instance (t2.medium) with Docker installation
- Encrypted EBS volumes
- User data scripts for initial setup

## Usage

```hcl
module "ec2" {
  source = "../../modules/compute/ec2"

  project_name              = "baiker"
  environment              = "dev"
  vpc_id                   = module.vpc.vpc_id
  public_subnet_ids        = module.vpc.public_subnet_ids
  bastion_security_group_id = module.security_groups.ssh_bastion_sg_id
  main_security_group_id   = module.security_groups.main_sg_id
  key_pair_name            = "baiker-dev-key-pair-ec2"
  
  common_tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |
| tls | ~> 4.0 |
| local | ~> 2.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |
| tls | ~> 4.0 |
| local | ~> 2.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| project_name | Name of the project for resource naming | `string` | n/a | yes |
| environment | Environment name (dev, staging, prod) | `string` | n/a | yes |
| vpc_id | ID of the VPC | `string` | n/a | yes |
| public_subnet_ids | List of public subnet IDs | `list(string)` | n/a | yes |
| bastion_security_group_id | Security group ID for bastion host | `string` | n/a | yes |
| main_security_group_id | Security group ID for main instance | `string` | n/a | yes |
| ami_id | AMI ID for EC2 instances | `string` | `"ami-054400ced365b82a0"` | no |
| bastion_instance_type | Instance type for bastion host | `string` | `"t2.micro"` | no |
| main_instance_type | Instance type for main instance | `string` | `"t2.medium"` | no |
| key_pair_name | Name for the key pair | `string` | n/a | yes |
| common_tags | Common tags to apply to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| key_pair_id | ID of the key pair |
| key_pair_name | Name of the key pair |
| private_key_pem | Private key in PEM format |
| bastion_instance_id | ID of the bastion instance |
| bastion_public_ip | Public IP of the bastion instance |
| bastion_private_ip | Private IP of the bastion instance |
| main_instance_id | ID of the main instance |
| main_public_ip | Public IP of the main instance |
| main_private_ip | Private IP of the main instance |
| instance_ids | Map of instance IDs |

## Notes

- The private key is automatically saved as a .pem file in the working directory
- Both instances use encrypted EBS volumes
- The main instance includes Docker installation via user data
- Instances are placed in the first public subnet
