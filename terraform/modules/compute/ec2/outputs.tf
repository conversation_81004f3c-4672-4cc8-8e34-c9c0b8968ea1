output "key_pair_id" {
  description = "ID of the key pair"
  value       = aws_key_pair.main.id
}

output "key_pair_name" {
  description = "Name of the key pair"
  value       = aws_key_pair.main.key_name
}

output "private_key_pem" {
  description = "Private key in PEM format"
  value       = tls_private_key.main.private_key_pem
  sensitive   = true
}

output "bastion_instance_id" {
  description = "ID of the bastion instance"
  value       = aws_instance.bastion.id
}

output "bastion_public_ip" {
  description = "Public IP of the bastion instance"
  value       = aws_instance.bastion.public_ip
}

output "bastion_private_ip" {
  description = "Private IP of the bastion instance"
  value       = aws_instance.bastion.private_ip
}

output "main_instance_id" {
  description = "ID of the main instance"
  value       = aws_instance.main.id
}

output "main_public_ip" {
  description = "Public IP of the main instance"
  value       = aws_instance.main.public_ip
}

output "main_private_ip" {
  description = "Private IP of the main instance"
  value       = aws_instance.main.private_ip
}

output "instance_ids" {
  description = "Map of instance IDs"
  value = {
    bastion = aws_instance.bastion.id
    main    = aws_instance.main.id
  }
}
