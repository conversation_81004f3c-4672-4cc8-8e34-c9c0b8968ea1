# Key Pair
resource "aws_key_pair" "main" {
  key_name   = var.key_pair_name
  public_key = tls_private_key.main.public_key_openssh

  tags = merge(var.common_tags, {
    Name = var.key_pair_name
  })
}

# Generate private key
resource "tls_private_key" "main" {
  algorithm = "ED25519"
}

# Save private key to local file
resource "local_file" "private_key" {
  content  = tls_private_key.main.private_key_openssh
  filename = "${var.key_pair_name}.pem"
  file_permission = "0600"
}

# User data for bastion host
locals {
  bastion_user_data = base64encode(<<-EOF
#!/bin/bash
apt update && apt upgrade -y && apt autoremove -y && apt install -y build-essential cmake make zip unzip tar wget curl vim
EOF
  )

  main_user_data = base64encode(<<-EOF
#!/bin/bash
apt update && apt upgrade -y && apt autoremove -y && apt install -y build-essential cmake make zip unzip tar wget curl vim

# Remove old Docker packages
for pkg in docker.io docker-doc docker-compose docker-compose-v2 podman-docker containerd runc; do sudo apt-get remove $pkg; done

# Add Docker's official GPG key:
sudo apt-get update
sudo apt-get install ca-certificates curl
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc

# Add the repository to Apt sources:
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
  $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update
sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin -y
usermod -aG docker $USER
EOF
  )
}

# Bastion Host Instance
resource "aws_instance" "bastion" {
  ami                    = var.ami_id
  instance_type          = var.bastion_instance_type
  key_name               = aws_key_pair.main.key_name
  vpc_security_group_ids = [var.bastion_security_group_id]
  subnet_id              = var.public_subnet_ids[0]
  
  associate_public_ip_address = true
  
  user_data = local.bastion_user_data

  root_block_device {
    volume_type = "gp3"
    volume_size = 20
    encrypted   = true
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-ec2-bastion-host"
  })
}

# Main Instance
resource "aws_instance" "main" {
  ami                    = var.ami_id
  instance_type          = var.main_instance_type
  key_name               = aws_key_pair.main.key_name
  vpc_security_group_ids = [var.main_security_group_id]
  subnet_id              = var.public_subnet_ids[0]
  
  associate_public_ip_address = true
  
  user_data = local.main_user_data

  root_block_device {
    volume_type = "gp3"
    volume_size = 30
    encrypted   = true
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-ec2"
  })
}
