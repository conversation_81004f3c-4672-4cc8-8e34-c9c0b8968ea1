# Load Balancer Terraform Module

This module creates a Network Load Balancer with multiple target groups for the Baiker application infrastructure.

## Features

- Network Load Balancer for high performance
- 6 target groups with different protocols and ports
- Health checks configured for each target group
- Automatic target group attachments for instance-based targets

## Target Groups Created

1. **HumanAI** (`baiker-dev-tg-humanai`)
   - Port: 9999, Protocol: TCP
   - Health check: HTTP /stream

2. **HumanAI SRS** (`baiker-dev-tg-humanai-srs`)
   - Port: 1986, Protocol: TCP
   - Health check: HTTP /stream

3. **HumanAI SRS UDP** (`baiker-dev-tg-humanai-srs-udp`)
   - Port: 5000, Protocol: UDP
   - Health check: TCP

4. **Web** (`baiker-dev-tg-web`)
   - Port: 80, Protocol: TCP, Target Type: IP
   - Health check: TCP /

5. **Web Backend** (`baiker-dev-tg-web-be`)
   - Port: 8001, Protocol: TCP
   - Health check: HTTP /

6. **App Backend** (`baiker-dev-tg-app-be`)
   - Port: 7005, Protocol: TCP, Target Type: IP
   - Health check: HTTP /

## Usage

```hcl
module "load_balancer" {
  source = "../../modules/networking/load-balancer"

  project_name      = "baiker"
  environment       = "dev"
  vpc_id            = module.vpc.vpc_id
  public_subnet_ids = module.vpc.public_subnet_ids
  main_instance_id  = module.ec2.main_instance_id
  
  common_tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| project_name | Name of the project for resource naming | `string` | n/a | yes |
| environment | Environment name (dev, staging, prod) | `string` | n/a | yes |
| vpc_id | ID of the VPC | `string` | n/a | yes |
| public_subnet_ids | List of public subnet IDs for load balancer | `list(string)` | n/a | yes |
| main_instance_id | ID of the main EC2 instance for target group attachments | `string` | n/a | yes |
| common_tags | Common tags to apply to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| load_balancer_id | ID of the Network Load Balancer |
| load_balancer_arn | ARN of the Network Load Balancer |
| load_balancer_dns_name | DNS name of the Network Load Balancer |
| load_balancer_zone_id | Zone ID of the Network Load Balancer |
| target_group_arns | ARNs of all target groups |
| target_group_ids | IDs of all target groups |
| humanai_target_group_arn | ARN of the HumanAI target group |
| humanai_srs_target_group_arn | ARN of the HumanAI SRS target group |
| humanai_srs_udp_target_group_arn | ARN of the HumanAI SRS UDP target group |
| web_target_group_arn | ARN of the Web target group |
| web_be_target_group_arn | ARN of the Web Backend target group |
| app_be_target_group_arn | ARN of the App Backend target group |

## Notes

- Instance-based target groups automatically attach to the main EC2 instance
- IP-based target groups (web and app_be) require manual IP registration
- Health check configurations follow the specifications in the requirements
