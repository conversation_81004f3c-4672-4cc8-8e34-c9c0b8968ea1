output "load_balancer_id" {
  description = "ID of the Network Load Balancer"
  value       = aws_lb.main.id
}

output "load_balancer_arn" {
  description = "ARN of the Network Load Balancer"
  value       = aws_lb.main.arn
}

output "load_balancer_dns_name" {
  description = "DNS name of the Network Load Balancer"
  value       = aws_lb.main.dns_name
}

output "load_balancer_zone_id" {
  description = "Zone ID of the Network Load Balancer"
  value       = aws_lb.main.zone_id
}

output "target_group_arns" {
  description = "ARNs of all target groups"
  value = {
    humanai         = aws_lb_target_group.humanai.arn
    humanai_srs     = aws_lb_target_group.humanai_srs.arn
    humanai_srs_udp = aws_lb_target_group.humanai_srs_udp.arn
    web             = aws_lb_target_group.web.arn
    web_be          = aws_lb_target_group.web_be.arn
    app_be          = aws_lb_target_group.app_be.arn
  }
}

output "target_group_ids" {
  description = "IDs of all target groups"
  value = {
    humanai         = aws_lb_target_group.humanai.id
    humanai_srs     = aws_lb_target_group.humanai_srs.id
    humanai_srs_udp = aws_lb_target_group.humanai_srs_udp.id
    web             = aws_lb_target_group.web.id
    web_be          = aws_lb_target_group.web_be.id
    app_be          = aws_lb_target_group.app_be.id
  }
}

output "humanai_target_group_arn" {
  description = "ARN of the HumanAI target group"
  value       = aws_lb_target_group.humanai.arn
}

output "humanai_srs_target_group_arn" {
  description = "ARN of the HumanAI SRS target group"
  value       = aws_lb_target_group.humanai_srs.arn
}

output "humanai_srs_udp_target_group_arn" {
  description = "ARN of the HumanAI SRS UDP target group"
  value       = aws_lb_target_group.humanai_srs_udp.arn
}

output "web_target_group_arn" {
  description = "ARN of the Web target group"
  value       = aws_lb_target_group.web.arn
}

output "web_be_target_group_arn" {
  description = "ARN of the Web Backend target group"
  value       = aws_lb_target_group.web_be.arn
}

output "app_be_target_group_arn" {
  description = "ARN of the App Backend target group"
  value       = aws_lb_target_group.app_be.arn
}
