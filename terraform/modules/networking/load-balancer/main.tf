# Network Load Balancer
resource "aws_lb" "main" {
  name               = "${var.project_name}-${var.environment}-nlb"
  internal           = false
  load_balancer_type = "network"
  subnets            = var.public_subnet_ids

  enable_deletion_protection = false

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-nlb"
  })
}

# Target Group 1: HumanAI
resource "aws_lb_target_group" "humanai" {
  name     = "${var.project_name}-${var.environment}-tg-humanai"
  port     = 9999
  protocol = "TCP"
  vpc_id   = var.vpc_id

  health_check {
    enabled             = true
    healthy_threshold   = 5
    unhealthy_threshold = 2
    timeout             = 5
    interval            = 60
    protocol            = "HTTP"
    path                = "/stream"
    matcher             = "200"
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-tg-humanai"
  })
}

# Target Group 2: HumanAI SRS
resource "aws_lb_target_group" "humanai_srs" {
  name     = "${var.project_name}-${var.environment}-tg-humanai-srs"
  port     = 1986
  protocol = "TCP"
  vpc_id   = var.vpc_id

  health_check {
    enabled             = true
    healthy_threshold   = 5
    unhealthy_threshold = 2
    timeout             = 5
    interval            = 60
    protocol            = "HTTP"
    path                = "/stream"
    matcher             = "200"
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-tg-humanai-srs"
  })
}

# Target Group 3: HumanAI SRS UDP
resource "aws_lb_target_group" "humanai_srs_udp" {
  name     = "${var.project_name}-${var.environment}-tg-humanai-srs-udp"
  port     = 5000
  protocol = "UDP"
  vpc_id   = var.vpc_id

  health_check {
    enabled             = true
    healthy_threshold   = 5
    unhealthy_threshold = 2
    timeout             = 5
    interval            = 60
    protocol            = "TCP"
    port                = "traffic-port"
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-tg-humanai-srs-udp"
  })
}

# Target Group 4: Web
resource "aws_lb_target_group" "web" {
  name        = "${var.project_name}-${var.environment}-tg-web"
  port        = 80
  protocol    = "TCP"
  vpc_id      = var.vpc_id
  target_type = "ip"

  health_check {
    enabled             = true
    healthy_threshold   = 5
    unhealthy_threshold = 2
    timeout             = 5
    interval            = 60
    protocol            = "TCP"
    path                = "/"
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-tg-web"
  })
}

# Target Group 5: Web Backend
resource "aws_lb_target_group" "web_be" {
  name     = "${var.project_name}-${var.environment}-tg-web-be"
  port     = 8001
  protocol = "TCP"
  vpc_id   = var.vpc_id

  health_check {
    enabled             = true
    healthy_threshold   = 5
    unhealthy_threshold = 2
    timeout             = 5
    interval            = 60
    protocol            = "HTTP"
    path                = "/"
    matcher             = "200"
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-tg-web-be"
  })
}

# Target Group 6: App Backend
resource "aws_lb_target_group" "app_be" {
  name        = "${var.project_name}-${var.environment}-tg-app-be"
  port        = 7005
  protocol    = "TCP"
  vpc_id      = var.vpc_id
  target_type = "ip"

  health_check {
    enabled             = true
    healthy_threshold   = 5
    unhealthy_threshold = 2
    timeout             = 5
    interval            = 60
    protocol            = "HTTP"
    path                = "/"
    matcher             = "200"
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-tg-app-be"
  })
}

# Target Group Attachments for Instance-based targets
resource "aws_lb_target_group_attachment" "humanai" {
  target_group_arn = aws_lb_target_group.humanai.arn
  target_id        = var.main_instance_id
  port             = 9999
}

resource "aws_lb_target_group_attachment" "humanai_srs" {
  target_group_arn = aws_lb_target_group.humanai_srs.arn
  target_id        = var.main_instance_id
  port             = 1986
}

resource "aws_lb_target_group_attachment" "humanai_srs_udp" {
  target_group_arn = aws_lb_target_group.humanai_srs_udp.arn
  target_id        = var.main_instance_id
  port             = 5000
}

resource "aws_lb_target_group_attachment" "web_be" {
  target_group_arn = aws_lb_target_group.web_be.arn
  target_id        = var.main_instance_id
  port             = 8001
}

# Load Balancer Listeners - MISSING CRITICAL COMPONENT
resource "aws_lb_listener" "humanai" {
  load_balancer_arn = aws_lb.main.arn
  port              = "9999"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.humanai.arn
  }
}

resource "aws_lb_listener" "humanai_srs" {
  load_balancer_arn = aws_lb.main.arn
  port              = "1986"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.humanai_srs.arn
  }
}

resource "aws_lb_listener" "humanai_srs_udp" {
  load_balancer_arn = aws_lb.main.arn
  port              = "5000"
  protocol          = "UDP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.humanai_srs_udp.arn
  }
}

resource "aws_lb_listener" "web" {
  load_balancer_arn = aws_lb.main.arn
  port              = "80"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.web.arn
  }
}

resource "aws_lb_listener" "web_be" {
  load_balancer_arn = aws_lb.main.arn
  port              = "8001"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.web_be.arn
  }
}

resource "aws_lb_listener" "app_be" {
  load_balancer_arn = aws_lb.main.arn
  port              = "7005"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.app_be.arn
  }
}
