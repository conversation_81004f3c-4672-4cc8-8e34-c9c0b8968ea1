# VPC Terraform Module

This module creates a VPC with public and private subnets across multiple availability zones, including NAT gateways for private subnet internet access.

## Features

- VPC with configurable CIDR block
- Public and private subnets across multiple AZs
- Internet Gateway for public subnet access
- NAT Gateway(s) for private subnet internet access
- Route tables and associations
- DNS hostname and resolution support
- Default Network ACL configuration

## Usage

```hcl
module "vpc" {
  source = "../../modules/networking/vpc"

  name                   = "baiker-dev-vpc"
  cidr_block            = "10.0.0.0/16"
  availability_zones    = ["ap-northeast-1a", "ap-northeast-1c"]
  public_subnet_cidrs   = ["********/24", "********/24"]
  private_subnet_cidrs  = ["*********/24", "*********/24", "*********/24", "*********/24"]
  
  enable_dns_hostnames  = true
  enable_dns_support    = true
  enable_nat_gateway    = true
  single_nat_gateway    = true
  
  common_tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| name | Name prefix for VPC resources | `string` | n/a | yes |
| cidr_block | CIDR block for VPC | `string` | `"10.0.0.0/16"` | no |
| availability_zones | List of availability zones | `list(string)` | n/a | yes |
| public_subnet_cidrs | CIDR blocks for public subnets | `list(string)` | n/a | yes |
| private_subnet_cidrs | CIDR blocks for private subnets | `list(string)` | n/a | yes |
| enable_dns_hostnames | Enable DNS hostnames in VPC | `bool` | `true` | no |
| enable_dns_support | Enable DNS support in VPC | `bool` | `true` | no |
| enable_nat_gateway | Enable NAT Gateway | `bool` | `true` | no |
| single_nat_gateway | Use single NAT Gateway for all private subnets | `bool` | `true` | no |
| common_tags | Common tags to apply to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| vpc_id | ID of the VPC |
| vpc_cidr_block | CIDR block of the VPC |
| internet_gateway_id | ID of the Internet Gateway |
| public_subnet_ids | IDs of the public subnets |
| private_subnet_ids | IDs of the private subnets |
| public_subnet_cidrs | CIDR blocks of the public subnets |
| private_subnet_cidrs | CIDR blocks of the private subnets |
| nat_gateway_ids | IDs of the NAT Gateways |
| public_route_table_id | ID of the public route table |
| private_route_table_ids | IDs of the private route tables |
| default_network_acl_id | ID of the default network ACL |
| availability_zones | List of availability zones used |
