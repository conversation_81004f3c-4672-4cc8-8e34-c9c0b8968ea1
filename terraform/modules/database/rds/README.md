# RDS PostgreSQL Terraform Module

This module creates an RDS PostgreSQL instance with managed credentials, enhanced monitoring, and proper security configurations.

## Features

- PostgreSQL 17.4 engine
- Managed credentials via AWS Secrets Manager
- Enhanced monitoring with IAM role
- Encrypted storage with GP3 performance
- Automated backups and maintenance windows
- Private subnet deployment
- Parameter group for PostgreSQL 17

## Usage

```hcl
module "rds" {
  source = "../../modules/database/rds"

  project_name       = "baiker"
  environment        = "dev"
  vpc_id             = module.vpc.vpc_id
  private_subnet_ids = module.vpc.private_subnet_ids
  security_group_ids = [module.security_groups.rds_postgres_sg_id]
  
  db_name     = "baiker_dev_db"
  db_username = "postgres"
  db_port     = 5434
  
  engine_version   = "17.4"
  instance_class   = "db.m7g.large"
  allocated_storage = 100
  max_allocated_storage = 300
  
  common_tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |
| random | ~> 3.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |
| random | ~> 3.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| project_name | Name of the project for resource naming | `string` | n/a | yes |
| environment | Environment name (dev, staging, prod) | `string` | n/a | yes |
| vpc_id | ID of the VPC | `string` | n/a | yes |
| private_subnet_ids | List of private subnet IDs for RDS subnet group | `list(string)` | n/a | yes |
| security_group_ids | List of security group IDs for RDS instance | `list(string)` | n/a | yes |
| db_name | Name of the database | `string` | `"baiker_dev_db"` | no |
| db_username | Master username for the database | `string` | `"postgres"` | no |
| db_port | Port for the database | `number` | `5434` | no |
| engine_version | PostgreSQL engine version | `string` | `"17.4"` | no |
| instance_class | RDS instance class | `string` | `"db.m7g.large"` | no |
| allocated_storage | Initial allocated storage in GiB | `number` | `100` | no |
| max_allocated_storage | Maximum allocated storage for autoscaling in GiB | `number` | `300` | no |
| storage_type | Storage type | `string` | `"gp3"` | no |
| iops | Provisioned IOPS | `number` | `3000` | no |
| storage_throughput | Storage throughput in MiB/s | `number` | `125` | no |
| backup_retention_period | Backup retention period in days | `number` | `7` | no |
| backup_window | Backup window | `string` | `"03:00-04:00"` | no |
| maintenance_window | Maintenance window | `string` | `"sun:04:00-sun:05:00"` | no |
| deletion_protection | Enable deletion protection | `bool` | `false` | no |
| common_tags | Common tags to apply to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| db_instance_id | RDS instance ID |
| db_instance_arn | RDS instance ARN |
| db_instance_endpoint | RDS instance endpoint |
| db_instance_hosted_zone_id | RDS instance hosted zone ID |
| db_instance_port | RDS instance port |
| db_instance_name | RDS instance database name |
| db_instance_username | RDS instance master username |
| db_subnet_group_id | DB subnet group ID |
| db_subnet_group_arn | DB subnet group ARN |
| db_parameter_group_id | DB parameter group ID |
| db_parameter_group_arn | DB parameter group ARN |
| secrets_manager_secret_id | Secrets Manager secret ID for RDS credentials |
| secrets_manager_secret_arn | Secrets Manager secret ARN for RDS credentials |
| rds_monitoring_role_arn | RDS monitoring IAM role ARN |
| db_connection_info | Database connection information |

## Security Features

- Credentials managed by AWS Secrets Manager
- Encrypted storage at rest
- Deployed in private subnets
- Security group restrictions
- Enhanced monitoring enabled
