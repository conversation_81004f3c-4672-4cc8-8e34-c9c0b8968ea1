variable "bucket_names" {
  description = "List of S3 bucket names to create"
  type        = list(string)
}

variable "project_name" {
  description = "Name of the project for resource naming"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "enable_versioning" {
  description = "Enable versioning for S3 buckets"
  type        = bool
  default     = false
}

variable "enable_encryption" {
  description = "Enable server-side encryption for S3 buckets"
  type        = bool
  default     = true
}

variable "kms_key_id" {
  description = "KMS key ID for S3 bucket encryption (optional)"
  type        = string
  default     = null
}

variable "block_public_access" {
  description = "Block all public access to S3 buckets"
  type        = bool
  default     = true
}

variable "enable_lifecycle_policy" {
  description = "Enable lifecycle policy for S3 buckets"
  type        = bool
  default     = false
}

variable "lifecycle_rules" {
  description = "List of lifecycle rules for S3 buckets"
  type = list(object({
    id                          = string
    enabled                     = bool
    abort_incomplete_multipart_upload_days = optional(number)
    expiration_days            = optional(number)
    noncurrent_version_expiration_days = optional(number)
    transitions = optional(list(object({
      days          = number
      storage_class = string
    })))
  }))
  default = []
}

variable "enable_access_logging" {
  description = "Enable access logging for S3 buckets"
  type        = bool
  default     = false
}

variable "access_log_bucket" {
  description = "S3 bucket for access logs"
  type        = string
  default     = null
}

variable "access_log_prefix" {
  description = "Prefix for access logs"
  type        = string
  default     = "access-logs/"
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}
