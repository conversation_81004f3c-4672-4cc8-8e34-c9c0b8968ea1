output "bucket_ids" {
  description = "List of S3 bucket IDs"
  value       = aws_s3_bucket.main[*].id
}

output "bucket_arns" {
  description = "List of S3 bucket ARNs"
  value       = aws_s3_bucket.main[*].arn
}

output "bucket_domain_names" {
  description = "List of S3 bucket domain names"
  value       = aws_s3_bucket.main[*].bucket_domain_name
}

output "bucket_regional_domain_names" {
  description = "List of S3 bucket regional domain names"
  value       = aws_s3_bucket.main[*].bucket_regional_domain_name
}

output "bucket_hosted_zone_ids" {
  description = "List of S3 bucket hosted zone IDs"
  value       = aws_s3_bucket.main[*].hosted_zone_id
}

output "bucket_regions" {
  description = "List of S3 bucket regions"
  value       = aws_s3_bucket.main[*].region
}

output "buckets_map" {
  description = "Map of bucket names to their properties"
  value = {
    for idx, bucket in aws_s3_bucket.main : bucket.id => {
      id                         = bucket.id
      arn                        = bucket.arn
      domain_name               = bucket.bucket_domain_name
      regional_domain_name      = bucket.bucket_regional_domain_name
      hosted_zone_id            = bucket.hosted_zone_id
      region                    = bucket.region
    }
  }
}

# Individual bucket outputs for common use cases
output "terraform_state_bucket_id" {
  description = "Terraform state bucket ID"
  value       = length([for bucket in aws_s3_bucket.main : bucket.id if can(regex("terraform-state", bucket.id))]) > 0 ? [for bucket in aws_s3_bucket.main : bucket.id if can(regex("terraform-state", bucket.id))][0] : null
}

output "terraform_state_bucket_arn" {
  description = "Terraform state bucket ARN"
  value       = length([for bucket in aws_s3_bucket.main : bucket.arn if can(regex("terraform-state", bucket.id))]) > 0 ? [for bucket in aws_s3_bucket.main : bucket.arn if can(regex("terraform-state", bucket.id))][0] : null
}

output "raw_data_bucket_id" {
  description = "Raw data bucket ID"
  value       = length([for bucket in aws_s3_bucket.main : bucket.id if can(regex("raw-data", bucket.id))]) > 0 ? [for bucket in aws_s3_bucket.main : bucket.id if can(regex("raw-data", bucket.id))][0] : null
}

output "raw_data_bucket_arn" {
  description = "Raw data bucket ARN"
  value       = length([for bucket in aws_s3_bucket.main : bucket.arn if can(regex("raw-data", bucket.id))]) > 0 ? [for bucket in aws_s3_bucket.main : bucket.arn if can(regex("raw-data", bucket.id))][0] : null
}
