# S3 Buckets
resource "aws_s3_bucket" "main" {
  count  = length(var.bucket_names)
  bucket = var.bucket_names[count.index]

  tags = merge(var.common_tags, {
    Name = var.bucket_names[count.index]
  })
}

# S3 Bucket Versioning
resource "aws_s3_bucket_versioning" "main" {
  count  = length(var.bucket_names)
  bucket = aws_s3_bucket.main[count.index].id
  
  versioning_configuration {
    status = var.enable_versioning ? "Enabled" : "Disabled"
  }
}

# S3 Bucket Server Side Encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "main" {
  count  = var.enable_encryption ? length(var.bucket_names) : 0
  bucket = aws_s3_bucket.main[count.index].id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm     = var.kms_key_id != null ? "aws:kms" : "AES256"
      kms_master_key_id = var.kms_key_id
    }
    bucket_key_enabled = var.kms_key_id != null ? true : false
  }
}

# S3 Bucket Public Access Block
resource "aws_s3_bucket_public_access_block" "main" {
  count  = var.block_public_access ? length(var.bucket_names) : 0
  bucket = aws_s3_bucket.main[count.index].id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# S3 Bucket ACL
resource "aws_s3_bucket_acl" "main" {
  count      = length(var.bucket_names)
  bucket     = aws_s3_bucket.main[count.index].id
  acl        = "private"
  depends_on = [aws_s3_bucket_ownership_controls.main]
}

# S3 Bucket Ownership Controls
resource "aws_s3_bucket_ownership_controls" "main" {
  count  = length(var.bucket_names)
  bucket = aws_s3_bucket.main[count.index].id

  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

# S3 Bucket Lifecycle Configuration
resource "aws_s3_bucket_lifecycle_configuration" "main" {
  count  = var.enable_lifecycle_policy && length(var.lifecycle_rules) > 0 ? length(var.bucket_names) : 0
  bucket = aws_s3_bucket.main[count.index].id

  dynamic "rule" {
    for_each = var.lifecycle_rules
    content {
      id     = rule.value.id
      status = rule.value.enabled ? "Enabled" : "Disabled"

      dynamic "abort_incomplete_multipart_upload" {
        for_each = rule.value.abort_incomplete_multipart_upload_days != null ? [1] : []
        content {
          days_after_initiation = rule.value.abort_incomplete_multipart_upload_days
        }
      }

      dynamic "expiration" {
        for_each = rule.value.expiration_days != null ? [1] : []
        content {
          days = rule.value.expiration_days
        }
      }

      dynamic "noncurrent_version_expiration" {
        for_each = rule.value.noncurrent_version_expiration_days != null ? [1] : []
        content {
          noncurrent_days = rule.value.noncurrent_version_expiration_days
        }
      }

      dynamic "transition" {
        for_each = rule.value.transitions != null ? rule.value.transitions : []
        content {
          days          = transition.value.days
          storage_class = transition.value.storage_class
        }
      }
    }
  }

  depends_on = [aws_s3_bucket_versioning.main]
}

# S3 Bucket Logging
resource "aws_s3_bucket_logging" "main" {
  count  = var.enable_access_logging && var.access_log_bucket != null ? length(var.bucket_names) : 0
  bucket = aws_s3_bucket.main[count.index].id

  target_bucket = var.access_log_bucket
  target_prefix = "${var.access_log_prefix}${var.bucket_names[count.index]}/"
}
