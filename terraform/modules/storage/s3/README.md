# S3 Terraform Module

This module creates multiple S3 buckets with configurable security, versioning, encryption, and lifecycle policies.

## Features

- Multiple S3 bucket creation
- Server-side encryption (AES-256 or KMS)
- Public access blocking
- Versioning control
- Lifecycle policies
- Access logging
- Ownership controls

## Usage

```hcl
module "s3" {
  source = "../../modules/storage/s3"

  bucket_names = [
    "baiker-dev-s3-terraform-state",
    "baiker-dev-s3-raw-data"
  ]
  
  project_name    = "baiker"
  environment     = "dev"
  
  enable_versioning      = false
  enable_encryption      = true
  block_public_access    = true
  enable_lifecycle_policy = false
  
  common_tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Usage with Lifecycle Policy

```hcl
module "s3" {
  source = "../../modules/storage/s3"

  bucket_names = [
    "baiker-dev-s3-terraform-state",
    "baiker-dev-s3-raw-data"
  ]
  
  project_name    = "baiker"
  environment     = "dev"
  
  enable_versioning      = true
  enable_encryption      = true
  block_public_access    = true
  enable_lifecycle_policy = true
  
  lifecycle_rules = [
    {
      id      = "delete_old_versions"
      enabled = true
      noncurrent_version_expiration_days = 30
      abort_incomplete_multipart_upload_days = 7
    },
    {
      id      = "transition_to_ia"
      enabled = true
      transitions = [
        {
          days          = 30
          storage_class = "STANDARD_IA"
        },
        {
          days          = 90
          storage_class = "GLACIER"
        }
      ]
    }
  ]
  
  common_tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| bucket_names | List of S3 bucket names to create | `list(string)` | n/a | yes |
| project_name | Name of the project for resource naming | `string` | n/a | yes |
| environment | Environment name (dev, staging, prod) | `string` | n/a | yes |
| enable_versioning | Enable versioning for S3 buckets | `bool` | `false` | no |
| enable_encryption | Enable server-side encryption for S3 buckets | `bool` | `true` | no |
| kms_key_id | KMS key ID for S3 bucket encryption (optional) | `string` | `null` | no |
| block_public_access | Block all public access to S3 buckets | `bool` | `true` | no |
| enable_lifecycle_policy | Enable lifecycle policy for S3 buckets | `bool` | `false` | no |
| lifecycle_rules | List of lifecycle rules for S3 buckets | `list(object)` | `[]` | no |
| enable_access_logging | Enable access logging for S3 buckets | `bool` | `false` | no |
| access_log_bucket | S3 bucket for access logs | `string` | `null` | no |
| access_log_prefix | Prefix for access logs | `string` | `"access-logs/"` | no |
| common_tags | Common tags to apply to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| bucket_ids | List of S3 bucket IDs |
| bucket_arns | List of S3 bucket ARNs |
| bucket_domain_names | List of S3 bucket domain names |
| bucket_regional_domain_names | List of S3 bucket regional domain names |
| bucket_hosted_zone_ids | List of S3 bucket hosted zone IDs |
| bucket_regions | List of S3 bucket regions |
| buckets_map | Map of bucket names to their properties |
| terraform_state_bucket_id | Terraform state bucket ID |
| terraform_state_bucket_arn | Terraform state bucket ARN |
| raw_data_bucket_id | Raw data bucket ID |
| raw_data_bucket_arn | Raw data bucket ARN |

## Security Features

- All public access blocked by default
- Server-side encryption enabled
- Private ACL enforced
- Bucket ownership controls configured
- Optional KMS encryption support
