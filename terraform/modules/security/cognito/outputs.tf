output "user_pool_id" {
  description = "ID of the Cognito User Pool"
  value       = aws_cognito_user_pool.main.id
}

output "user_pool_arn" {
  description = "ARN of the Cognito User Pool"
  value       = aws_cognito_user_pool.main.arn
}

output "user_pool_name" {
  description = "Name of the Cognito User Pool"
  value       = aws_cognito_user_pool.main.name
}

output "user_pool_endpoint" {
  description = "Endpoint name of the Cognito User Pool"
  value       = aws_cognito_user_pool.main.endpoint
}

output "user_pool_creation_date" {
  description = "Date the user pool was created"
  value       = aws_cognito_user_pool.main.creation_date
}

output "user_pool_last_modified_date" {
  description = "Date the user pool was last modified"
  value       = aws_cognito_user_pool.main.last_modified_date
}

output "user_pool_client_id" {
  description = "ID of the Cognito User Pool Client"
  value       = aws_cognito_user_pool_client.main.id
}

output "user_pool_client_name" {
  description = "Name of the Cognito User Pool Client"
  value       = aws_cognito_user_pool_client.main.name
}

output "user_pool_client_secret" {
  description = "Secret of the Cognito User Pool Client"
  value       = aws_cognito_user_pool_client.main.client_secret
  sensitive   = true
}

output "user_pool_domain" {
  description = "Domain of the Cognito User Pool"
  value       = aws_cognito_user_pool_domain.main.domain
}

output "user_pool_domain_aws_account_id" {
  description = "AWS account ID of the Cognito User Pool Domain"
  value       = aws_cognito_user_pool_domain.main.aws_account_id
}

output "user_pool_domain_cloudfront_distribution_arn" {
  description = "CloudFront distribution ARN of the Cognito User Pool Domain"
  value       = aws_cognito_user_pool_domain.main.cloudfront_distribution_arn
}

output "user_pool_domain_s3_bucket" {
  description = "S3 bucket of the Cognito User Pool Domain"
  value       = aws_cognito_user_pool_domain.main.s3_bucket
}

output "cognito_info" {
  description = "Complete Cognito configuration information"
  value = {
    user_pool_id     = aws_cognito_user_pool.main.id
    user_pool_arn    = aws_cognito_user_pool.main.arn
    client_id        = aws_cognito_user_pool_client.main.id
    domain           = aws_cognito_user_pool_domain.main.domain
    endpoint         = aws_cognito_user_pool.main.endpoint
  }
}
