# Cognito User Pool
resource "aws_cognito_user_pool" "main" {
  name = var.user_pool_name

  # Sign-in options
  alias_attributes         = var.alias_attributes
  auto_verified_attributes = var.auto_verified_attributes

  # Password policy
  password_policy {
    minimum_length    = 6
    require_lowercase = true
    require_numbers   = true
    require_symbols   = true
    require_uppercase = true
  }

  # Account recovery
  account_recovery_setting {
    recovery_mechanism {
      name     = "verified_email"
      priority = 1
    }
  }

  # Email configuration
  email_configuration {
    email_sending_account = "COGNITO_DEFAULT"
  }

  # User pool add-ons
  user_pool_add_ons {
    advanced_security_mode = "ENFORCED"
  }

  # Username configuration
  username_configuration {
    case_sensitive = false
  }

  # Verification message template
  verification_message_template {
    default_email_option = "CONFIRM_WITH_CODE"
  }

  tags = merge(var.common_tags, {
    Name = var.user_pool_name
  })
}

# Cognito User Pool Client
resource "aws_cognito_user_pool_client" "main" {
  name         = var.app_client_name
  user_pool_id = aws_cognito_user_pool.main.id

  # Client settings
  generate_secret = false

  # Authentication flows
  explicit_auth_flows = var.explicit_auth_flows

  # Token validity
  auth_session_validity         = var.authentication_flow_session_duration
  refresh_token_validity        = var.refresh_token_validity
  access_token_validity         = var.access_token_validity
  id_token_validity            = var.id_token_validity

  # Token validity units
  token_validity_units {
    refresh_token = "days"
    access_token  = "minutes"
    id_token      = "minutes"
  }

  # Security settings
  enable_token_revocation       = var.enable_token_revocation
  prevent_user_existence_errors = var.prevent_user_existence_errors ? "ENABLED" : "DISABLED"

  # OAuth settings
  allowed_oauth_flows                  = var.oauth_flows
  allowed_oauth_scopes                 = var.oauth_scopes
  allowed_oauth_flows_user_pool_client = true

  # Supported identity providers
  supported_identity_providers = ["COGNITO"]

  depends_on = [aws_cognito_user_pool.main]
}

# Cognito User Pool Domain (optional)
resource "aws_cognito_user_pool_domain" "main" {
  domain       = "${var.project_name}-${var.environment}-auth"
  user_pool_id = aws_cognito_user_pool.main.id

  depends_on = [aws_cognito_user_pool.main]
}
