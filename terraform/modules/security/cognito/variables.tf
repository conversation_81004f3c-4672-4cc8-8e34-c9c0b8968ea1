variable "project_name" {
  description = "Name of the project for resource naming"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "user_pool_name" {
  description = "Name of the Cognito User Pool"
  type        = string
}

variable "app_client_name" {
  description = "Name of the Cognito App Client"
  type        = string
}

variable "alias_attributes" {
  description = "Attributes supported as an alias for this user pool"
  type        = list(string)
  default     = ["email", "preferred_username"]
}

variable "auto_verified_attributes" {
  description = "Attributes to be auto-verified"
  type        = list(string)
  default     = ["email"]
}

variable "authentication_flow_session_duration" {
  description = "Authentication flow session duration in minutes"
  type        = number
  default     = 3
}

variable "refresh_token_validity" {
  description = "Refresh token validity in days"
  type        = number
  default     = 5
}

variable "access_token_validity" {
  description = "Access token validity in minutes"
  type        = number
  default     = 60
}

variable "id_token_validity" {
  description = "ID token validity in minutes"
  type        = number
  default     = 60
}

variable "enable_token_revocation" {
  description = "Enable token revocation"
  type        = bool
  default     = true
}

variable "prevent_user_existence_errors" {
  description = "Prevent user existence errors"
  type        = bool
  default     = true
}

variable "oauth_scopes" {
  description = "List of OAuth 2.0 scopes"
  type        = list(string)
  default     = ["email", "profile", "openid"]
}

variable "oauth_flows" {
  description = "List of OAuth 2.0 flows"
  type        = list(string)
  default     = ["code"]
}

variable "explicit_auth_flows" {
  description = "List of authentication flows"
  type        = list(string)
  default     = ["ALLOW_USER_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"]
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}
