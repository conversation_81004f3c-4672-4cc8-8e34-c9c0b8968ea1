# Cognito Terraform Module

This module creates a Cognito User Pool with an app client configured for mobile applications with email and username sign-in support.

## Features

- Cognito User Pool with email and username aliases
- Mobile app client configuration
- Advanced security features enabled
- OAuth 2.0 support with authorization code flow
- Configurable token validity periods
- User pool domain for hosted UI
- Password policy enforcement
- Account recovery via email

## Usage

```hcl
module "cognito" {
  source = "../../modules/security/cognito"

  project_name    = "baiker"
  environment     = "dev"
  user_pool_name  = "baiker-dev-cognito"
  app_client_name = "baiker-dev-cognito-app-client"
  
  # Authentication settings
  authentication_flow_session_duration = 3
  refresh_token_validity               = 5
  access_token_validity                = 60
  id_token_validity                   = 60
  
  # Security settings
  enable_token_revocation         = true
  prevent_user_existence_errors   = true
  
  # OAuth settings
  oauth_scopes = ["email", "profile", "openid"]
  oauth_flows  = ["code"]
  
  # Authentication flows
  explicit_auth_flows = [
    "ALLOW_USER_AUTH",
    "ALLOW_USER_SRP_AUTH", 
    "ALLOW_REFRESH_TOKEN_AUTH"
  ]
  
  common_tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| project_name | Name of the project for resource naming | `string` | n/a | yes |
| environment | Environment name (dev, staging, prod) | `string` | n/a | yes |
| user_pool_name | Name of the Cognito User Pool | `string` | n/a | yes |
| app_client_name | Name of the Cognito App Client | `string` | n/a | yes |
| alias_attributes | Attributes supported as an alias for this user pool | `list(string)` | `["email", "preferred_username"]` | no |
| auto_verified_attributes | Attributes to be auto-verified | `list(string)` | `["email"]` | no |
| authentication_flow_session_duration | Authentication flow session duration in minutes | `number` | `3` | no |
| refresh_token_validity | Refresh token validity in days | `number` | `5` | no |
| access_token_validity | Access token validity in minutes | `number` | `60` | no |
| id_token_validity | ID token validity in minutes | `number` | `60` | no |
| enable_token_revocation | Enable token revocation | `bool` | `true` | no |
| prevent_user_existence_errors | Prevent user existence errors | `bool` | `true` | no |
| oauth_scopes | List of OAuth 2.0 scopes | `list(string)` | `["email", "profile", "openid"]` | no |
| oauth_flows | List of OAuth 2.0 flows | `list(string)` | `["code"]` | no |
| explicit_auth_flows | List of authentication flows | `list(string)` | `["ALLOW_USER_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"]` | no |
| common_tags | Common tags to apply to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| user_pool_id | ID of the Cognito User Pool |
| user_pool_arn | ARN of the Cognito User Pool |
| user_pool_name | Name of the Cognito User Pool |
| user_pool_endpoint | Endpoint name of the Cognito User Pool |
| user_pool_creation_date | Date the user pool was created |
| user_pool_last_modified_date | Date the user pool was last modified |
| user_pool_client_id | ID of the Cognito User Pool Client |
| user_pool_client_name | Name of the Cognito User Pool Client |
| user_pool_client_secret | Secret of the Cognito User Pool Client |
| user_pool_domain | Domain of the Cognito User Pool |
| user_pool_domain_aws_account_id | AWS account ID of the Cognito User Pool Domain |
| user_pool_domain_cloudfront_distribution_arn | CloudFront distribution ARN of the Cognito User Pool Domain |
| user_pool_domain_s3_bucket | S3 bucket of the Cognito User Pool Domain |
| cognito_info | Complete Cognito configuration information |

## Security Features

- Advanced security mode enforced
- Strong password policy requirements
- Token revocation support
- User existence error prevention
- Email verification required
- Case-insensitive usernames
