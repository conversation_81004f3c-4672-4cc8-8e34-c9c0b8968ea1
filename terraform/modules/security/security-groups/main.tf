# Security Group 1: Main application security group
resource "aws_security_group" "main" {
  name        = "${var.project_name}-${var.environment}-sg"
  description = "Allow Port to EC2 using GPU"
  vpc_id      = var.vpc_id

  # SSH from bastion
  ingress {
    description     = "SSH from bastion"
    from_port       = 22
    to_port         = 22
    protocol        = "tcp"
    security_groups = [aws_security_group.ssh_bastion.id]
  }

  # Custom TCP 9999
  ingress {
    description = "Custom TCP 9999"
    from_port   = 9999
    to_port     = 9999
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Custom TCP 1986
  ingress {
    description = "Custom TCP 1986"
    from_port   = 1986
    to_port     = 1986
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Custom TCP 8003
  ingress {
    description = "Custom TCP 8003"
    from_port   = 8003
    to_port     = 8003
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Custom UDP 5000
  ingress {
    description = "Custom UDP 5000"
    from_port   = 5000
    to_port     = 5000
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-sg"
  })
}

# Security Group 2: SSH Bastion
resource "aws_security_group" "ssh_bastion" {
  name        = "${var.project_name}-${var.environment}-sg-ssh-bastion"
  description = "Allow SSH access to developers"
  vpc_id      = var.vpc_id

  # SSH from allowed CIDR
  ingress {
    description = "SSH from allowed CIDR"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [var.allowed_ssh_cidr]
  }

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-sg-ssh-bastion"
  })
}

# Security Group 3: RDS PostgreSQL
resource "aws_security_group" "rds_postgres" {
  name        = "${var.project_name}-${var.environment}-sg-rds-postgre"
  description = "Allow connect to Postgres Database"
  vpc_id      = var.vpc_id

  # PostgreSQL from allowed CIDR
  ingress {
    description = "PostgreSQL from allowed CIDR"
    from_port   = 5454
    to_port     = 5454
    protocol    = "tcp"
    cidr_blocks = [var.allowed_ssh_cidr]
  }

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-sg-rds-postgre"
  })
}

# Security Group 4: Redis Database
resource "aws_security_group" "redis_db" {
  name        = "${var.project_name}-${var.environment}-redis-db"
  description = "Allow connect to MemoryDB Redis"
  vpc_id      = var.vpc_id

  # Redis from allowed CIDR
  ingress {
    description = "Redis from allowed CIDR"
    from_port   = 6369
    to_port     = 6369
    protocol    = "tcp"
    cidr_blocks = [var.allowed_ssh_cidr]
  }

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-redis-db"
  })
}

# Security Group 5: Network Load Balancer
resource "aws_security_group" "nlb" {
  name        = "${var.project_name}-${var.environment}-sg-nlb"
  description = "Allow Port for Network Load Balancer"
  vpc_id      = var.vpc_id

  # Custom TCP 4999
  ingress {
    description = "Custom TCP 4999"
    from_port   = 4999
    to_port     = 4999
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Custom TCP 9999
  ingress {
    description = "Custom TCP 9999"
    from_port   = 9999
    to_port     = 9999
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Custom TCP 1986
  ingress {
    description = "Custom TCP 1986"
    from_port   = 1986
    to_port     = 1986
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Custom TCP 8003
  ingress {
    description = "Custom TCP 8003"
    from_port   = 8003
    to_port     = 8003
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Custom UDP 5000
  ingress {
    description = "Custom UDP 5000"
    from_port   = 5000
    to_port     = 5000
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # All outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.common_tags, {
    Name = "${var.project_name}-${var.environment}-sg-nlb"
  })
}
