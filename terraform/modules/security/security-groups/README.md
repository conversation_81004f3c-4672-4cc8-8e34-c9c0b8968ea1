# Security Groups Terraform Module

This module creates security groups for the Baiker application infrastructure with specific ingress and egress rules.

## Security Groups Created

1. **Main Security Group** (`baiker-dev-sg`)
   - SSH access from bastion host
   - Custom TCP ports: 9999, 1986, 8003
   - Custom UDP port: 5000
   - All outbound traffic allowed

2. **SSH Bastion Security Group** (`baiker-dev-sg-ssh-bastion`)
   - SSH access from specific CIDR (*************/32)
   - All outbound traffic allowed

3. **RDS PostgreSQL Security Group** (`baiker-dev-sg-rds-postgre`)
   - PostgreSQL access on port 5454 from specific CIDR
   - All outbound traffic allowed

4. **Redis Database Security Group** (`baiker-dev-redis-db`)
   - Redis access on port 6369 from specific CIDR
   - All outbound traffic allowed

5. **Network Load Balancer Security Group** (`baiker-dev-sg-nlb`)
   - Custom TCP ports: 4999, 9999, 1986, 8003
   - Custom UDP port: 5000
   - All outbound traffic allowed

## Usage

```hcl
module "security_groups" {
  source = "../../modules/security/security-groups"

  vpc_id           = module.vpc.vpc_id
  project_name     = "baiker"
  environment      = "dev"
  allowed_ssh_cidr = "*************/32"
  
  common_tags = {
    Environment = "dev"
    Project     = "baiker"
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| vpc_id | ID of the VPC where security groups will be created | `string` | n/a | yes |
| project_name | Name of the project for resource naming | `string` | n/a | yes |
| environment | Environment name (dev, staging, prod) | `string` | n/a | yes |
| allowed_ssh_cidr | CIDR block allowed for SSH access | `string` | `"*************/32"` | no |
| common_tags | Common tags to apply to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| main_sg_id | ID of the main security group |
| ssh_bastion_sg_id | ID of the SSH bastion security group |
| rds_postgres_sg_id | ID of the RDS PostgreSQL security group |
| redis_db_sg_id | ID of the Redis database security group |
| nlb_sg_id | ID of the Network Load Balancer security group |
| security_group_ids | Map of all security group IDs |
