output "main_sg_id" {
  description = "ID of the main security group"
  value       = aws_security_group.main.id
}

output "ssh_bastion_sg_id" {
  description = "ID of the SSH bastion security group"
  value       = aws_security_group.ssh_bastion.id
}

output "rds_postgres_sg_id" {
  description = "ID of the RDS PostgreSQL security group"
  value       = aws_security_group.rds_postgres.id
}

output "redis_db_sg_id" {
  description = "ID of the Redis database security group"
  value       = aws_security_group.redis_db.id
}

output "nlb_sg_id" {
  description = "ID of the Network Load Balancer security group"
  value       = aws_security_group.nlb.id
}

output "security_group_ids" {
  description = "Map of all security group IDs"
  value = {
    main        = aws_security_group.main.id
    ssh_bastion = aws_security_group.ssh_bastion.id
    rds_postgres = aws_security_group.rds_postgres.id
    redis_db    = aws_security_group.redis_db.id
    nlb         = aws_security_group.nlb.id
  }
}
