# Common includes for all terragrunt files
include "root" {
  path = find_in_parent_folders()
}

include "deployment" {
  path = find_in_parent_folders("deployment.hcl")
}

include "area" {
  path = find_in_parent_folders("area.hcl")
}

include "region" {
  path = find_in_parent_folders("region.hcl")
}

locals {
  deployment_vars = read_terragrunt_config(find_in_parent_folders("deployment.hcl"))
  area_vars       = read_terragrunt_config(find_in_parent_folders("area.hcl"))
  region_vars     = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  
  deployment = local.deployment_vars.locals.deployment
  project    = local.deployment_vars.locals.project
  area       = local.area_vars.locals.area
  region     = local.region_vars.locals.aws_region
}