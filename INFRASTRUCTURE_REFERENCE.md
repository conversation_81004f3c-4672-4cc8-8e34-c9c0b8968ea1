# Baiker Infrastructure Reference

Quick reference for the deployed infrastructure components and their configurations.

## Network Architecture

### VPC Configuration
- **Name**: `baiker-dev-vpc`
- **CIDR**: `10.0.0.0/16`
- **Availability Zones**: `ap-northeast-1a`, `ap-northeast-1c`
- **DNS**: Hostnames and resolution enabled

### Subnets
| Type | CIDR | AZ | Purpose |
|------|------|----|---------| 
| Public | ********/24 | ap-northeast-1a | Bastion, Main EC2, NLB |
| Public | ********/24 | ap-northeast-1c | NLB redundancy |
| Private | *********/24 | ap-northeast-1a | RDS, internal services |
| Private | *********/24 | ap-northeast-1c | RDS, internal services |
| Private | *********/24 | ap-northeast-1a | Future expansion |
| Private | *********/24 | ap-northeast-1c | Future expansion |

### NAT Gateway
- **Count**: 1 (in ap-northeast-1a)
- **Purpose**: Internet access for private subnets

## Security Groups

### 1. Main Security Group (`baiker-dev-sg`)
**Purpose**: Main application instance security
- **Inbound**:
  - SSH (22) from bastion security group
  - TCP 9999 from 0.0.0.0/0
  - TCP 1986 from 0.0.0.0/0  
  - TCP 8003 from 0.0.0.0/0
  - UDP 5000 from 0.0.0.0/0
- **Outbound**: All traffic to 0.0.0.0/0

### 2. SSH Bastion Security Group (`baiker-dev-sg-ssh-bastion`)
**Purpose**: Bastion host access control
- **Inbound**: SSH (22) from *************/32
- **Outbound**: All traffic to 0.0.0.0/0

### 3. RDS PostgreSQL Security Group (`baiker-dev-sg-rds-postgre`)
**Purpose**: Database access control
- **Inbound**: TCP 5454 from *************/32
- **Outbound**: All traffic to 0.0.0.0/0

### 4. Redis Database Security Group (`baiker-dev-redis-db`)
**Purpose**: Redis/MemoryDB access control
- **Inbound**: TCP 6369 from *************/32
- **Outbound**: All traffic to 0.0.0.0/0

### 5. Network Load Balancer Security Group (`baiker-dev-sg-nlb`)
**Purpose**: Load balancer traffic control
- **Inbound**:
  - TCP 4999 from 0.0.0.0/0
  - TCP 9999 from 0.0.0.0/0
  - TCP 1986 from 0.0.0.0/0
  - TCP 8003 from 0.0.0.0/0
  - UDP 5000 from 0.0.0.0/0
- **Outbound**: All traffic to 0.0.0.0/0

## Compute Resources

### EC2 Instances

#### Bastion Host (`baiker-dev-ec2-bastion-host`)
- **Instance Type**: t2.micro
- **AMI**: ami-054400ced365b82a0 (Ubuntu 24.04)
- **Subnet**: Public subnet (********/24)
- **Security Group**: baiker-dev-sg-ssh-bastion
- **Storage**: 20 GiB GP3 encrypted
- **Purpose**: SSH gateway for private resources

#### Main Instance (`baiker-dev-ec2`)
- **Instance Type**: t2.medium
- **AMI**: ami-054400ced365b82a0 (Ubuntu 24.04)
- **Subnet**: Public subnet (********/24)
- **Security Group**: baiker-dev-sg
- **Storage**: 30 GiB GP3 encrypted
- **Features**: Docker pre-installed
- **Purpose**: Main application server

### Key Pair
- **Name**: `baiker-dev-key-pair-ec2`
- **Type**: ED25519
- **Format**: .pem file (auto-generated)

## Database

### RDS PostgreSQL (`baiker-dev-db`)
- **Engine**: PostgreSQL 17.4
- **Instance Class**: db.m7g.large
- **Storage**: 100 GiB GP3 (auto-scaling to 300 GiB)
- **IOPS**: 3000
- **Throughput**: 125 MiB/s
- **Port**: 5434 (custom)
- **Database Name**: baiker_dev_db
- **Username**: postgres
- **Credentials**: AWS Secrets Manager
- **Backup**: 7 days retention
- **Maintenance Window**: Sunday 04:00-05:00 UTC
- **Backup Window**: 03:00-04:00 UTC

## Load Balancing

### Network Load Balancer (`baiker-dev-nlb`)
- **Type**: Network Load Balancer
- **Scheme**: Internet-facing
- **Subnets**: Both public subnets

### Target Groups

| Name | Port | Protocol | Target Type | Health Check |
|------|------|----------|-------------|--------------|
| baiker-dev-tg-humanai | 9999 | TCP | Instance | HTTP /stream |
| baiker-dev-tg-humanai-srs | 1986 | TCP | Instance | HTTP /stream |
| baiker-dev-tg-humanai-srs-udp | 5000 | UDP | Instance | TCP |
| baiker-dev-tg-web | 80 | TCP | IP | TCP / |
| baiker-dev-tg-web-be | 8001 | TCP | Instance | HTTP / |
| baiker-dev-tg-app-be | 7005 | TCP | IP | HTTP / |

## Storage

### S3 Buckets

#### Terraform State (`baiker-dev-s3-terraform-state`)
- **Purpose**: Terraform state storage
- **Versioning**: Disabled
- **Encryption**: AES-256
- **Public Access**: Blocked

#### Raw Data (`baiker-dev-s3-raw-data`)
- **Purpose**: Application data storage
- **Versioning**: Disabled
- **Encryption**: AES-256
- **Public Access**: Blocked

### ECR Repository (`dev/baiker`)
- **Image Mutability**: Mutable
- **Encryption**: AES-256
- **Scan on Push**: Enabled
- **Lifecycle Policy**: Keep last 10 tagged images, delete untagged after 1 day

## Authentication

### Cognito User Pool (`baiker-dev-cognito`)
- **Sign-in Options**: Email, Username
- **App Client**: baiker-dev-cognito-app-client
- **Application Type**: Mobile app
- **Authentication Flows**:
  - ALLOW_USER_AUTH
  - ALLOW_USER_SRP_AUTH
  - ALLOW_REFRESH_TOKEN_AUTH
- **Token Validity**:
  - Access Token: 60 minutes
  - ID Token: 60 minutes
  - Refresh Token: 5 days
  - Auth Session: 3 minutes
- **OAuth Scopes**: email, profile, openid
- **OAuth Flows**: Authorization code grant
- **Domain**: baiker-dev-auth.auth.ap-northeast-1.amazoncognito.com

## Access Information

### SSH Access
```bash
# Connect to bastion
ssh -i baiker-dev-key-pair-ec2.pem ubuntu@<bastion-public-ip>

# From bastion to main instance
ssh -i baiker-dev-key-pair-ec2.pem ubuntu@<main-private-ip>
```

### Database Access
```bash
# Get credentials from Secrets Manager
aws secretsmanager get-secret-value --secret-id baiker-dev-rds-credentials

# Connect to database
psql -h <rds-endpoint> -p 5454 -U postgres -d baiker_dev_db
```

### Container Registry
```bash
# Login to ECR
aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.ap-northeast-1.amazonaws.com

# Push image
docker tag myapp:latest <account-id>.dkr.ecr.ap-northeast-1.amazonaws.com/dev/baiker:latest
docker push <account-id>.dkr.ecr.ap-northeast-1.amazonaws.com/dev/baiker:latest
```

## Monitoring Endpoints

- **Load Balancer DNS**: Check terragrunt outputs
- **RDS Endpoint**: Check terragrunt outputs or AWS console
- **Cognito Domain**: baiker-dev-auth.auth.ap-northeast-1.amazoncognito.com
