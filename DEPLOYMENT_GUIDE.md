# Baiker Infrastructure Deployment Guide

This guide provides step-by-step instructions for deploying the Baiker infrastructure using Terraform and Terragrunt.

## Prerequisites

### Required Software
- **Terraform** >= 1.0
- **Terragrunt** >= 0.45
- **AWS CLI** >= 2.0
- **Git**

### AWS Account Requirements
- AWS account with administrative permissions
- AWS CLI configured with appropriate credentials
- Access to ap-northeast-1 region

## Pre-Deployment Setup

### 1. Install Dependencies

#### Ubuntu/Debian
```bash
# Install Terraform
curl -fsSL https://apt.releases.hashicorp.com/gpg | sudo apt-key add -
sudo apt-add-repository "deb [arch=amd64] https://apt.releases.hashicorp.com $(lsb_release -cs) main"
sudo apt-get update && sudo apt-get install terraform

# Install Terragrunt
curl -Lo terragrunt https://github.com/gruntwork-io/terragrunt/releases/latest/download/terragrunt_linux_amd64
chmod +x terragrunt
sudo mv terragrunt /usr/local/bin/

# Verify installations
terraform --version
terragrunt --version
```

#### macOS
```bash
# Using Homebrew
brew install terraform terragrunt

# Verify installations
terraform --version
terragrunt --version
```

### 2. Configure AWS Credentials

```bash
# Configure AWS CLI
aws configure

# Verify configuration
aws sts get-caller-identity
aws ec2 describe-regions --region ap-northeast-1
```

### 3. Clone Repository

```bash
git clone <repository-url>
cd Entanglement_IaC
```

## Deployment Process

### Option 1: Deploy All Services at Once (Recommended for New Deployments)

```bash
# Navigate to dev environment
cd deployments/dev/aws-services/ap-northeast-1

# Plan all changes
terragrunt run-all plan

# Apply all changes (will handle dependencies automatically)
terragrunt run-all apply
```

### Option 2: Deploy Services Individually (Recommended for Production)

Deploy in the following order to respect dependencies:

```bash
cd deployments/dev/aws-services/ap-northeast-1

# 1. VPC (Foundation)
cd vpc
terragrunt plan
terragrunt apply
cd ..

# 2. Security Groups (Depends on VPC)
cd security-groups
terragrunt plan
terragrunt apply
cd ..

# 3. EC2 Instances (Depends on VPC and Security Groups)
cd ec2
terragrunt plan
terragrunt apply
cd ..

# 4. RDS Database (Depends on VPC and Security Groups)
cd rds
terragrunt plan
terragrunt apply
cd ..

# 5. Load Balancer (Depends on VPC and EC2)
cd load-balancer
terragrunt plan
terragrunt apply
cd ..

# 6. ECR Repository (Independent)
cd ecr
terragrunt plan
terragrunt apply
cd ..

# 7. Cognito User Pool (Independent)
cd cognito
terragrunt plan
terragrunt apply
cd ..

# 8. S3 Buckets (Independent)
cd s3
terragrunt plan
terragrunt apply
cd ..
```

## Post-Deployment Steps

### 1. Verify Infrastructure

```bash
# Check all resources are created
terragrunt run-all output

# Verify specific components
aws ec2 describe-instances --region ap-northeast-1
aws rds describe-db-instances --region ap-northeast-1
aws elbv2 describe-load-balancers --region ap-northeast-1
```

### 2. Access EC2 Instances

```bash
# The private key is automatically generated and saved as .pem file
# Find the key file in the ec2 deployment directory
cd deployments/dev/aws-services/ap-northeast-1/ec2

# Connect to bastion host
ssh -i baiker-dev-key-pair-ec2.pem ubuntu@<bastion-public-ip>

# From bastion, connect to main instance
ssh -i baiker-dev-key-pair-ec2.pem ubuntu@<main-private-ip>
```

### 3. Database Access

```bash
# Retrieve database credentials from AWS Secrets Manager
aws secretsmanager get-secret-value \
  --secret-id baiker-dev-rds-credentials \
  --region ap-northeast-1 \
  --query SecretString --output text | jq .

# Connect to database (from within VPC)
psql -h <rds-endpoint> -p 5434 -U postgres -d baiker_dev_db
```

## Monitoring and Maintenance

### Health Checks

```bash
# Check Terragrunt state
terragrunt run-all validate

# Check AWS resources
aws ec2 describe-instance-status --region ap-northeast-1
aws rds describe-db-instances --region ap-northeast-1 --query 'DBInstances[*].DBInstanceStatus'
```

### Updates and Changes

```bash
# Plan changes
terragrunt run-all plan

# Apply changes
terragrunt run-all apply

# For specific module updates
cd <module-directory>
terragrunt plan
terragrunt apply
```

## Troubleshooting

### Common Issues

1. **State Lock Issues**
   ```bash
   # Force unlock (use with caution)
   terragrunt force-unlock <lock-id>
   ```

2. **Dependency Errors**
   ```bash
   # Deploy dependencies first
   terragrunt run-all plan --terragrunt-include-dependencies
   ```

3. **Permission Errors**
   ```bash
   # Verify AWS permissions
   aws iam get-user
   aws sts get-caller-identity
   ```

4. **Resource Conflicts**
   ```bash
   # Import existing resources
   terragrunt import <resource-type>.<resource-name> <aws-resource-id>
   ```

### Logs and Debugging

```bash
# Enable debug logging
export TF_LOG=DEBUG
export TERRAGRUNT_LOG_LEVEL=debug

# Run with verbose output
terragrunt apply --terragrunt-log-level debug
```

## Cleanup

### Destroy Infrastructure

```bash
# Destroy all resources (use with extreme caution)
cd deployments/dev/aws-services/ap-northeast-1
terragrunt run-all destroy

# Or destroy specific modules in reverse order
cd s3 && terragrunt destroy
cd ../cognito && terragrunt destroy
cd ../ecr && terragrunt destroy
cd ../load-balancer && terragrunt destroy
cd ../rds && terragrunt destroy
cd ../ec2 && terragrunt destroy
cd ../security-groups && terragrunt destroy
cd ../vpc && terragrunt destroy
```

## Security Considerations

- Private key files are generated locally - secure them appropriately
- RDS credentials are stored in AWS Secrets Manager
- All S3 buckets block public access by default
- Security groups restrict access to specific IP ranges
- Consider enabling AWS CloudTrail for audit logging

## Next Steps

After successful deployment:
1. Configure application deployments to ECR
2. Set up monitoring and alerting
3. Configure backup strategies
4. Implement CI/CD pipelines
5. Set up log aggregation
